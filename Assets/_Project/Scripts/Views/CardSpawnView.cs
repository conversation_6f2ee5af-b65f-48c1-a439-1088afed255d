using Cysharp.Threading.Tasks;
using UnityEngine;

public struct CardSpawnEvent : IEvent
{
    public CardData Card;
}

public class CardSpawnView : BaseView
{
    [SerializeField] private Transform spawnParent;
    
    void Start()
    {
        Subscribe<CardSpawnEvent>(CreateCard);
        
        //  TODO: Replace by direct spawning
        ScanLevelForCardPositionData();
    }

    //  Temporary method which extracts cards positions and matches them with their key indices in order to accurately spawn them when necessary
    //  TODO: Replace by proper spawn logic
    private void ScanLevelForCardPositionData()
    {
        var cardViews = GetComponentsInChildren<PuzzleCardView>();
        foreach (var cardView in cardViews)
        {
            Game.DataManager.Session.CurrentPuzzle.CardPositions.Add(cardView.VisualCardIndex, cardView.transform);
        }
    }

    private async UniTask CreateCard(CardSpawnEvent evt)
    {
        var cardTransform = Game.DataManager.Session.CurrentPuzzle.CardPositions[evt.Card.VisualCardIndex];
        var cardAsset = await Game.AssetManager.GetCardPrefabByName(evt.Card);

        // Use Unity's async instantiation for better performance
        var asyncInstantiateOperation = Object.InstantiateAsync(cardAsset, cardTransform.position, cardTransform.rotation, spawnParent);
        var instantiatedObjects = await asyncInstantiateOperation.ToUniTask();

        var card = instantiatedObjects[0] as GameObject;
        card.GetComponent<PuzzleCardView>().Init(evt.Card);
    }
}
